// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		EC821FD12E37BA4B003C4364 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = EC821FBB2E37BA4A003C4364 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EC821FC22E37BA4A003C4364;
			remoteInfo = "My Ios App";
		};
		EC821FDB2E37BA4B003C4364 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = EC821FBB2E37BA4A003C4364 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EC821FC22E37BA4A003C4364;
			remoteInfo = "My Ios App";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		EC821FC32E37BA4A003C4364 /* My Ios App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "My Ios App.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		EC821FD02E37BA4B003C4364 /* My Ios AppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "My Ios AppTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		EC821FDA2E37BA4B003C4364 /* My Ios AppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "My Ios AppUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		EC821FC52E37BA4A003C4364 /* My Ios App */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "My Ios App";
			sourceTree = "<group>";
		};
		EC821FD32E37BA4B003C4364 /* My Ios AppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "My Ios AppTests";
			sourceTree = "<group>";
		};
		EC821FDD2E37BA4B003C4364 /* My Ios AppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "My Ios AppUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		EC821FC02E37BA4A003C4364 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EC821FCD2E37BA4B003C4364 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EC821FD72E37BA4B003C4364 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		EC821FBA2E37BA4A003C4364 = {
			isa = PBXGroup;
			children = (
				EC821FC52E37BA4A003C4364 /* My Ios App */,
				EC821FD32E37BA4B003C4364 /* My Ios AppTests */,
				EC821FDD2E37BA4B003C4364 /* My Ios AppUITests */,
				EC821FC42E37BA4A003C4364 /* Products */,
			);
			sourceTree = "<group>";
		};
		EC821FC42E37BA4A003C4364 /* Products */ = {
			isa = PBXGroup;
			children = (
				EC821FC32E37BA4A003C4364 /* My Ios App.app */,
				EC821FD02E37BA4B003C4364 /* My Ios AppTests.xctest */,
				EC821FDA2E37BA4B003C4364 /* My Ios AppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		EC821FC22E37BA4A003C4364 /* My Ios App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EC821FE42E37BA4B003C4364 /* Build configuration list for PBXNativeTarget "My Ios App" */;
			buildPhases = (
				EC821FBF2E37BA4A003C4364 /* Sources */,
				EC821FC02E37BA4A003C4364 /* Frameworks */,
				EC821FC12E37BA4A003C4364 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				EC821FC52E37BA4A003C4364 /* My Ios App */,
			);
			name = "My Ios App";
			packageProductDependencies = (
			);
			productName = "My Ios App";
			productReference = EC821FC32E37BA4A003C4364 /* My Ios App.app */;
			productType = "com.apple.product-type.application";
		};
		EC821FCF2E37BA4B003C4364 /* My Ios AppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EC821FE72E37BA4B003C4364 /* Build configuration list for PBXNativeTarget "My Ios AppTests" */;
			buildPhases = (
				EC821FCC2E37BA4B003C4364 /* Sources */,
				EC821FCD2E37BA4B003C4364 /* Frameworks */,
				EC821FCE2E37BA4B003C4364 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				EC821FD22E37BA4B003C4364 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				EC821FD32E37BA4B003C4364 /* My Ios AppTests */,
			);
			name = "My Ios AppTests";
			packageProductDependencies = (
			);
			productName = "My Ios AppTests";
			productReference = EC821FD02E37BA4B003C4364 /* My Ios AppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		EC821FD92E37BA4B003C4364 /* My Ios AppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EC821FEA2E37BA4B003C4364 /* Build configuration list for PBXNativeTarget "My Ios AppUITests" */;
			buildPhases = (
				EC821FD62E37BA4B003C4364 /* Sources */,
				EC821FD72E37BA4B003C4364 /* Frameworks */,
				EC821FD82E37BA4B003C4364 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				EC821FDC2E37BA4B003C4364 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				EC821FDD2E37BA4B003C4364 /* My Ios AppUITests */,
			);
			name = "My Ios AppUITests";
			packageProductDependencies = (
			);
			productName = "My Ios AppUITests";
			productReference = EC821FDA2E37BA4B003C4364 /* My Ios AppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		EC821FBB2E37BA4A003C4364 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					EC821FC22E37BA4A003C4364 = {
						CreatedOnToolsVersion = 16.4;
					};
					EC821FCF2E37BA4B003C4364 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = EC821FC22E37BA4A003C4364;
					};
					EC821FD92E37BA4B003C4364 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = EC821FC22E37BA4A003C4364;
					};
				};
			};
			buildConfigurationList = EC821FBE2E37BA4A003C4364 /* Build configuration list for PBXProject "My Ios App" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = EC821FBA2E37BA4A003C4364;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = EC821FC42E37BA4A003C4364 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				EC821FC22E37BA4A003C4364 /* My Ios App */,
				EC821FCF2E37BA4B003C4364 /* My Ios AppTests */,
				EC821FD92E37BA4B003C4364 /* My Ios AppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		EC821FC12E37BA4A003C4364 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EC821FCE2E37BA4B003C4364 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EC821FD82E37BA4B003C4364 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		EC821FBF2E37BA4A003C4364 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EC821FCC2E37BA4B003C4364 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EC821FD62E37BA4B003C4364 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		EC821FD22E37BA4B003C4364 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EC821FC22E37BA4A003C4364 /* My Ios App */;
			targetProxy = EC821FD12E37BA4B003C4364 /* PBXContainerItemProxy */;
		};
		EC821FDC2E37BA4B003C4364 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EC821FC22E37BA4A003C4364 /* My Ios App */;
			targetProxy = EC821FDB2E37BA4B003C4364 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		EC821FE22E37BA4B003C4364 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		EC821FE32E37BA4B003C4364 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		EC821FE52E37BA4B003C4364 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ben-s-Workspace.My-Ios-App";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		EC821FE62E37BA4B003C4364 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ben-s-Workspace.My-Ios-App";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		EC821FE82E37BA4B003C4364 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ben-s-Workspace.My-Ios-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/My Ios App.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/My Ios App";
			};
			name = Debug;
		};
		EC821FE92E37BA4B003C4364 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ben-s-Workspace.My-Ios-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/My Ios App.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/My Ios App";
			};
			name = Release;
		};
		EC821FEB2E37BA4B003C4364 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ben-s-Workspace.My-Ios-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "My Ios App";
			};
			name = Debug;
		};
		EC821FEC2E37BA4B003C4364 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ben-s-Workspace.My-Ios-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "My Ios App";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		EC821FBE2E37BA4A003C4364 /* Build configuration list for PBXProject "My Ios App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EC821FE22E37BA4B003C4364 /* Debug */,
				EC821FE32E37BA4B003C4364 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EC821FE42E37BA4B003C4364 /* Build configuration list for PBXNativeTarget "My Ios App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EC821FE52E37BA4B003C4364 /* Debug */,
				EC821FE62E37BA4B003C4364 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EC821FE72E37BA4B003C4364 /* Build configuration list for PBXNativeTarget "My Ios AppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EC821FE82E37BA4B003C4364 /* Debug */,
				EC821FE92E37BA4B003C4364 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EC821FEA2E37BA4B003C4364 /* Build configuration list for PBXNativeTarget "My Ios AppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EC821FEB2E37BA4B003C4364 /* Debug */,
				EC821FEC2E37BA4B003C4364 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = EC821FBB2E37BA4A003C4364 /* Project object */;
}
