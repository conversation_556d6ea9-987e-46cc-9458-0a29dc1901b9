#!/bin/bash

# 自动更新README文件的脚本

echo "🚀 开始更新README文件..."

# 获取当前日期
CURRENT_DATE=$(date '+%Y-%m-%d')
echo "📅 当前日期: $CURRENT_DATE"

# 获取最新提交信息
LATEST_COMMIT=$(git log -1 --pretty=format:"%h - %s (%cr)" 2>/dev/null || echo "无法获取提交信息")
echo "📝 最新提交: $LATEST_COMMIT"

# 获取总提交数
COMMIT_COUNT=$(git rev-list --count HEAD 2>/dev/null || echo "0")
echo "📊 总提交数: $COMMIT_COUNT"

# 获取项目文件统计
SWIFT_FILES=$(find . -name "*.swift" -not -path "./.*" | wc -l | tr -d ' ')
echo "📁 Swift文件数: $SWIFT_FILES"

# 备份原README文件
cp README.md README.md.backup
echo "💾 已备份原README文件"

# 更新最后更新时间
sed -i.tmp "s/\*最后更新时间:.*\*/\*最后更新时间: $CURRENT_DATE\*/" README.md

# 检查是否存在项目统计部分，如果不存在则添加
if ! grep -q "## 项目统计" README.md; then
    # 在"## 贡献"之前添加统计信息
    sed -i.tmp '/## 贡献/i\
## 项目统计\
\
- 📊 总提交数: '$COMMIT_COUNT'\
- 📝 最新提交: '$LATEST_COMMIT'\
- 📁 Swift文件数: '$SWIFT_FILES'\
- 📅 最后统计: '$CURRENT_DATE'\
' README.md
else
    # 更新现有统计信息
    sed -i.tmp "s/- 📊 总提交数:.*/- 📊 总提交数: $COMMIT_COUNT/" README.md
    sed -i.tmp "s/- 📝 最新提交:.*/- 📝 最新提交: $LATEST_COMMIT/" README.md
    sed -i.tmp "s/- 📁 Swift文件数:.*/- 📁 Swift文件数: $SWIFT_FILES/" README.md
    sed -i.tmp "s/- 📅 最后统计:.*/- 📅 最后统计: $CURRENT_DATE/" README.md
fi

# 清理临时文件
rm -f README.md.tmp

echo "✅ README文件更新完成！"

# 询问是否要提交更改
read -p "🤔 是否要提交这些更改到Git? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git add README.md
    git commit -m "📝 自动更新README文件 - $CURRENT_DATE"
    echo "✅ 已提交更改到Git"
    
    read -p "🚀 是否要推送到远程仓库? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git push origin main
        echo "🎉 已推送到远程仓库！"
    fi
else
    echo "ℹ️  更改未提交，你可以稍后手动提交"
fi

echo "🎯 脚本执行完成！"
