#!/usr/bin/swift

import Foundation

// README自动更新Swift脚本

struct ProjectStats {
    let commitCount: Int
    let latestCommit: String
    let swiftFileCount: Int
    let lastUpdate: String
}

class ReadmeUpdater {
    
    func updateReadme() {
        print("🚀 开始更新README文件...")
        
        let stats = getProjectStats()
        
        guard let readmeContent = readReadmeFile() else {
            print("❌ 无法读取README.md文件")
            return
        }
        
        let updatedContent = updateReadmeContent(readmeContent, with: stats)
        
        if writeReadmeFile(updatedContent) {
            print("✅ README文件更新完成！")
            print("📊 统计信息:")
            print("   - 总提交数: \(stats.commitCount)")
            print("   - Swift文件数: \(stats.swiftFileCount)")
            print("   - 更新时间: \(stats.lastUpdate)")
        } else {
            print("❌ 写入README文件失败")
        }
    }
    
    private func getProjectStats() -> ProjectStats {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let currentDate = dateFormatter.string(from: Date())
        
        // 获取提交数
        let commitCount = runShellCommand("git rev-list --count HEAD") ?? "0"
        
        // 获取最新提交
        let latestCommit = runShellCommand("git log -1 --pretty=format:'%h - %s (%cr)'") ?? "无法获取提交信息"
        
        // 获取Swift文件数
        let swiftFileCount = runShellCommand("find . -name '*.swift' -not -path './.*' | wc -l") ?? "0"
        
        return ProjectStats(
            commitCount: Int(commitCount.trimmingCharacters(in: .whitespacesAndNewlines)) ?? 0,
            latestCommit: latestCommit,
            swiftFileCount: Int(swiftFileCount.trimmingCharacters(in: .whitespacesAndNewlines)) ?? 0,
            lastUpdate: currentDate
        )
    }
    
    private func runShellCommand(_ command: String) -> String? {
        let process = Process()
        let pipe = Pipe()
        
        process.standardOutput = pipe
        process.standardError = pipe
        process.arguments = ["-c", command]
        process.launchPath = "/bin/bash"
        
        do {
            try process.run()
            process.waitUntilExit()
            
            let data = pipe.fileHandleForReading.readDataToEndOfFile()
            return String(data: data, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines)
        } catch {
            return nil
        }
    }
    
    private func readReadmeFile() -> String? {
        do {
            return try String(contentsOfFile: "README.md", encoding: .utf8)
        } catch {
            return nil
        }
    }
    
    private func writeReadmeFile(_ content: String) -> Bool {
        do {
            try content.write(toFile: "README.md", atomically: true, encoding: .utf8)
            return true
        } catch {
            return false
        }
    }
    
    private func updateReadmeContent(_ content: String, with stats: ProjectStats) -> String {
        var updatedContent = content
        
        // 更新最后更新时间
        let datePattern = "\\*最后更新时间:.*\\*"
        let newDateString = "*最后更新时间: \(stats.lastUpdate)*"
        updatedContent = updatedContent.replacingOccurrences(
            of: datePattern,
            with: newDateString,
            options: .regularExpression
        )
        
        // 更新或添加项目统计
        if updatedContent.contains("## 项目统计") {
            // 更新现有统计
            updatedContent = updateExistingStats(updatedContent, with: stats)
        } else {
            // 添加新的统计部分
            updatedContent = addNewStatsSection(updatedContent, with: stats)
        }
        
        return updatedContent
    }
    
    private func updateExistingStats(_ content: String, with stats: ProjectStats) -> String {
        var updatedContent = content
        
        updatedContent = updatedContent.replacingOccurrences(
            of: "- 📊 总提交数:.*",
            with: "- 📊 总提交数: \(stats.commitCount)",
            options: .regularExpression
        )
        
        updatedContent = updatedContent.replacingOccurrences(
            of: "- 📝 最新提交:.*",
            with: "- 📝 最新提交: \(stats.latestCommit)",
            options: .regularExpression
        )
        
        updatedContent = updatedContent.replacingOccurrences(
            of: "- 📁 Swift文件数:.*",
            with: "- 📁 Swift文件数: \(stats.swiftFileCount)",
            options: .regularExpression
        )
        
        return updatedContent
    }
    
    private func addNewStatsSection(_ content: String, with stats: ProjectStats) -> String {
        let statsSection = """
        
        ## 项目统计
        
        - 📊 总提交数: \(stats.commitCount)
        - 📝 最新提交: \(stats.latestCommit)
        - 📁 Swift文件数: \(stats.swiftFileCount)
        - 📅 最后统计: \(stats.lastUpdate)
        
        """
        
        return content.replacingOccurrences(of: "## 贡献", with: statsSection + "## 贡献")
    }
}

// 运行更新器
let updater = ReadmeUpdater()
updater.updateReadme()
